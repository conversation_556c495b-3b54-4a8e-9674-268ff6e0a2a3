.\objects\main.o: ..\Source\src\main.c
.\objects\main.o: ..\Source\inc\appmain.h
.\objects\main.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\main.o: ..\Library\CMSIS\core_cm4.h
.\objects\main.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\main.o: ..\Library\CMSIS\core_cmInstr.h
.\objects\main.o: ..\Library\CMSIS\core_cmFunc.h
.\objects\main.o: ..\Library\CMSIS\core_cm4_simd.h
.\objects\main.o: ..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\main.o: ..\Source\inc\gd32f4xx_libopt.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\main.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\main.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\main.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
.\objects\main.o: ..\Source\inc\systick.h
.\objects\main.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdio.h
.\objects\main.o: ..\Source\inc\main.h
.\objects\main.o: ..\bsp\inc\bsp_gpio.h
.\objects\main.o: ..\bsp\inc\bsp_flash.h
.\objects\main.o: ..\Source\inc\INS_Data.h
.\objects\main.o: ..\Library\CMSIS\arm_math.h
.\objects\main.o: ..\Library\CMSIS\core_cm4.h
.\objects\main.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\string.h
.\objects\main.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\math.h
.\objects\main.o: ..\Source\inc\gnss.h
.\objects\main.o: ..\Common\inc\data_convert.h
.\objects\main.o: ..\Source\inc\tlhtype.h
.\objects\main.o: ..\Source\inc\can_data.h
.\objects\main.o: ..\Source\inc\imu_data.h
.\objects\main.o: ..\Source\inc\INS_sys.h
.\objects\main.o: ..\Source\src\INS912AlgorithmEntry.h
.\objects\main.o: ..\bsp\inc\bsp_sys.h
.\objects\main.o: ..\Library\CMSIS\core_cm4.h
.\objects\main.o: ..\bsp\inc\bsp_rtc.h
.\objects\main.o: ..\Source\inc\Time_unify.h
.\objects\main.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\time.h
.\objects\main.o: ..\bsp\inc\bsp_can.h
.\objects\main.o: ..\bsp\inc\bsp_fwdgt.h
.\objects\main.o: ..\bsp\inc\CH395SPI.H
.\objects\main.o: ..\bsp\inc\CH395INC.H
.\objects\main.o: ..\bsp\inc\CH395CMD.H
.\objects\main.o: ..\Source\inc\TCPServer.h
.\objects\main.o: ..\bsp\inc\bsp_fmc.h
.\objects\main.o: ..\bsp\inc\bsp_exti.h
.\objects\main.o: ..\bsp\inc\bmp280.h
.\objects\main.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdbool.h
.\objects\main.o: ..\bsp\inc\bmp2.h
.\objects\main.o: ..\bsp\inc\bmp2_defs.h
.\objects\main.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stddef.h
.\objects\main.o: ..\bsp\inc\common.h
.\objects\main.o: ..\bsp\inc\CH378_HAL.h
.\objects\main.o: ..\bsp\inc\CH378INC.H
.\objects\main.o: ..\bsp\inc\logger.h
.\objects\main.o: ..\bsp\inc\CH378_HAL.h
.\objects\main.o: ..\bsp\inc\FILE_SYS.h
.\objects\main.o: ..\bsp\inc\CH378_HAL.H
.\objects\main.o: ..\bsp\inc\bsp_tim.h
.\objects\main.o: ..\Source\inc\fpgad.h
.\objects\main.o: ..\Source\src\appdefine.h
.\objects\main.o: ..\Protocol\serial.h
.\objects\main.o: ..\Protocol\insdef.h
.\objects\main.o: ..\Protocol\config.h
.\objects\main.o: ..\Protocol\computerFrameParse.h
.\objects\main.o: ..\Source\src\gdtypedefine.h
.\objects\main.o: ..\Protocol\frame_analysis.h
.\objects\main.o: ..\Source\inc\deviceconfig.h
.\objects\main.o: ..\Source\src\datado.h
.\objects\main.o: ..\INAV\ins.h
.\objects\main.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\ctype.h
.\objects\main.o: ..\INAV\DATASTRUCT.h
.\objects\main.o: ..\INAV\CONST.h
.\objects\main.o: ..\INAV\TYPEDEFINE.h
.\objects\main.o: ..\INAV\FUNCTION.h
.\objects\main.o: ..\INAV\GLOBALDATA.h
.\objects\main.o: ..\Protocol\insTestingEntry.h
.\objects\main.o: ..\Source\src\datado.h
