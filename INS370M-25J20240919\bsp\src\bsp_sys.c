/**
  ******************************************************************************
  * @file    bsp_sys.c
  * <AUTHOR> Xu
  * @brief   This file provides the HID core functions.
  *
  * @verbatim
  *
  *          ===================================================================
  *                                TEMPLATE Class  Description
  *          ===================================================================
  *
  *
  *
  *
  *
  *
  * @note     In HS mode and when the DMA is used, all variables and data structures
  *           dealing with the DMA during the transaction process should be 32-bit aligned.
  *
  *
  *  @endverbatim
  *
  ******************************************************************************
  * @attention
  *
  * <h2><center>&copy; Copyright (c) Arthur Xu.
  * All rights reserved.</center></h2>
  *
  * This software component is licensed by Arthur Xu pravite license
  * the "License"; You may not use this file except in compliance with
  * the License. 
  *
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "bsp_sys.h"



/** @addtogroup ArthurXu
  * @{
  */


/** @defgroup bsp_sys
  * @brief usbd core module
  * @{
  */

/** @defgroup Private_TypesDefinitions
  * @{
  */
/**
  * @}
  */


/** @defgroup Private_Defines
  * @{
  */
#define bsp_sys_Global


/**
  * @}
  */


/** @defgroup Private_Macros
  * @{
  */

/**
  * @}
  */




/** @defgroup Private_FunctionPrototypes
  * @{
  */

/**
  * @}
  */

/** @defgroup Private_Variables
  * @{
  */
//static u8  fac_us=0;//us延时倍乘数
//static u16 fac_ms=0;//ms延时倍乘数


#if defined ( __ICCARM__ ) /*!< IAR Compiler */
  #pragma data_alignment=4
#endif
/* XXXXX TEMPLATE device Configuration Descriptor */


  /**********  Descriptor of TEMPLATE interface 0 Alternate setting 0 **************/


#if defined ( __ICCARM__ ) /*!< IAR Compiler */
  #pragma data_alignment=4
#endif
/* XXXXX Standard Device Descriptor */


/**
  * @}
  */

/** @defgroup Private_Functions
  * @{
  */

/**
  * @brief  bsp_sys_Init
  *         Initialize the XXXXX interface
  * @param  
  * @param  
  * @retval status
  */
/* 
void Cache_Enable(void)
{
    SCB_EnableICache();
    SCB_EnableDCache();
	SCB->CACR|=1<<2;   //强制D-Cache透写,如不开启,实际使用中可能遇到各种问题
	
}
*/
u8 IsICahceEnable(void)
{
    u8 sta;
    sta=((SCB->CCR)>>17)&0X01;
    return sta;
}

u8 IsDCahceEnable(void)
{
    u8 sta;
    sta=((SCB->CCR)>>16)&0X01;
    return sta;
}

u32 GetSoftwareVersion(void)
{
    return SoftwareVersion;
}

#if defined(__clang__)
void __attribute__((noinline)) WFI_SET(void)
{
    __asm__("wfi");
}

void __attribute__((noinline)) INTX_DISABLE(void)
{
    __asm__("cpsid i \t\n"
            "bx lr");
}

void __attribute__((noinline)) INTX_ENABLE(void)
{
    __asm__("cpsie i \t\n"
            "bx lr");
}

void __attribute__((noinline)) MSR_MSP(u32 addr) 
{
    __asm__("msr msp, r0 \t\n"
            "bx r14");
}
#elif defined (__CC_ARM)
__asm void WFI_SET(void)
{
	WFI;		  
}

__asm void INTX_DISABLE(void)
{
	CPSID   I
	BX      LR	  
}

__asm void INTX_ENABLE(void)
{
	CPSIE   I
	BX      LR  
}

__asm void MSR_MSP(u32 addr) 
{
	MSR MSP, r0 			//set Main Stack value
	BX r14
}
#endif

void Sys_Soft_Reset(void)
{   
	SCB->AIRCR =0X05FA0000|(u32)0x04;	  
}

#if CHIP_USED == USE_CHIP_STM32 
void delay_us(u32 nus)
{
	u32 temp;
	SysTick->LOAD=nus*fac_us; //时间加载
	SysTick->VAL=0x00;        //清空计数器
	SysTick->CTRL|=SysTick_CTRL_ENABLE_Msk ;          //开始倒数
	do
	{
		temp=SysTick->CTRL;
	}
	while(temp&0x01&&!(temp&(1<<16)));//等待时间到达   
	SysTick->CTRL&=~SysTick_CTRL_ENABLE_Msk;       //关闭计数器
	SysTick->VAL =0X00;       //清空计数器
}

void delay_init()
{
	HAL_SYSTICK_CLKSourceConfig(RCC_HCLK_DIV8);
	fac_us=SystemCoreClock/8000000;	//为系统时钟的1/8  
	fac_ms=(u16)fac_us*1000;//非ucos下,代表每个ms需要的systick时钟数   
}

void delay_ms(u16 nms)
{	 		  	  
	u32 temp;		   
	SysTick->LOAD=(u32)nms*fac_ms;//时间加载(SysTick->LOAD为24bit)
	SysTick->VAL =0x00;           //清空计数器
	SysTick->CTRL|=SysTick_CTRL_ENABLE_Msk ;          //开始倒数  
	do
	{
		temp=SysTick->CTRL;
	}
	while(temp&0x01&&!(temp&(1<<16)));//等待时间到达   
	SysTick->CTRL&=~SysTick_CTRL_ENABLE_Msk;       //关闭计数器
	SysTick->VAL =0X00;       //清空计数器
} 
#endif

/*
void JTAG_Set(u8 mode)
{
    u32 temp;
    temp=mode;
    temp<<=25;
    RCC->APB2ENR|=1<<0;     //开启辅助时钟
    AFIO->MAPR&=0XF8FFFFFF; //清除MAPR的[26:24]
    AFIO->MAPR|=temp;       //设置jtag模式
}
*/

/**
  * @}
  */


/**
  * @}
  */


/**
  * @}
  */

/************************ (C) COPYRIGHT Arthur Xu*****END OF FILE****/
