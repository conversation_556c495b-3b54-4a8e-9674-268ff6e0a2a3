/*!
    \file  	main.c
    \brief 	ins912-3a project
	\author	Bill
	\data	2023/10/27
*/
#include "appmain.h"
#include "ins.h"
#include "DATASTRUCT.h"
#include "FUNCTION.h"
#include "GLOBALDATA.h"
#include "insTestingEntry.h"
#include "datado.h"

int main(void)
{
	SysInit();				// Init inertial navigation device...
	
	SysInit_Over();		//系统初始化完成后，进入循环前的准备
	
	while(1) 
	{		
		if(fpga_syn) 
		{	//每一帧FPGA数据产生，处理
			fpga_syn = 0;
			
			get_fpgadata();							//1、获取当前帧FPGA数据，及相关
			AlgorithmDo();							//2、对获取的数据进行算法处理						
			INS912_Output(&gnavout);    //4、算法处理完成的数据，进行打包、发送处理			
		} 
		
		loopDoOther();								//循环中，处理其它事宜

		//analysisRxdata();							//
    

	}
}



