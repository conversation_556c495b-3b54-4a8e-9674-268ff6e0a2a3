# INS370M-25J20240919 新协议使用指南

## 🎯 概述

本指南介绍如何使用从INS600-21A移植过来的新协议功能，包括升级、版本查询、参数下发等完整功能。

## 🚀 快速开始

### 1. 系统初始化

在主程序中添加初始化调用：

```c
#include "SetParaBao.h"

int main(void)
{
    // 系统基础初始化
    SystemInit();
    
    // 初始化新协议模块
    SetParaBao_Init();
    
    // 其他初始化...
    
    while(1)
    {
        // 主循环
    }
}
```

### 2. 协议处理集成

协议处理已自动集成到`frameParse()`函数中，无需额外代码。

## 📡 协议格式

### 基本帧格式
```
+--------+--------+--------+--------+--------+--------+--------+--------+
| 帧头1  | 帧头2  | 帧头3  | 命令高 | 命令低 | 长度高 | 长度低 | 数据... |
+--------+--------+--------+--------+--------+--------+--------+--------+
|  0xFA  |  0x55  |  0xAF  |  CMD_H |  CMD_L | LEN_H  | LEN_L  |  DATA   |
+--------+--------+--------+--------+--------+--------+--------+--------+

+--------+--------+--------+
| 校验码 | 帧尾1  | 帧尾2  |
+--------+--------+--------+
|  CRC   |  0x00  |  0xFF  |
+--------+--------+--------+
```

## 🔧 主要功能使用

### 1. 软件升级功能

#### 升级流程：
1. **发送升级开始命令** (0x51AA)
2. **分包发送升级数据** (0x52AA)
3. **发送升级完成命令** (0x55AA)

#### 升级开始命令示例：
```
FA 55 AF 51 AA 00 F6 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 CRC 00 FF
```

#### 升级数据包示例：
```
FA 55 AF 52 AA 00 F6 [包序号2字节] [总包数2字节] [数据长度1字节] [升级数据128字节] [预留111字节] [帧计数2字节] CRC 00 FF
```

### 2. 版本查询功能

#### 发送版本查询命令 (0xF5AA)：
```
FA 55 AF F5 AA 00 F6 01 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 00 CRC 00 FF
```

#### 版本信息回应格式：
```
FA 55 AF F5 AA 00 F6 [反馈标志1字节] [主版本1字节] [次版本1字节] [修订号1字节] [版本日期4字节] [方案1字节] [后缀1字节] [客户版1字节] [帧计数2字节] [预留235字节] CRC 00 FF
```

### 3. 参数设置功能

#### 设置波特率 (0x13AA)：
```c
// 波特率对照表
// 1: 9600, 2: 19200, 3: 38400, 4: 57600
// 5: 115200, 6: 230400, 7: 460800, 8: 614400
```

#### 设置输出频率 (0x14AA)：
```c
// 频率值直接设置，单位：Hz
// 例如：200 表示200Hz输出频率
```

#### 设置GNSS杆臂参数 (0x18AA)：
```
FA 55 AF 18 AA 00 F6 [armX 4字节float] [armY 4字节float] [armZ 4字节float] [预留234字节] CRC 00 FF
```

### 4. 参数管理功能

#### 固化参数 (0xF1AA)：
```
FA 55 AF F1 AA 00 F6 01 [预留245字节] CRC 00 FF
```

#### 恢复出厂设置 (0xF2AA)：
```
FA 55 AF F2 AA 00 F6 01 [预留245字节] CRC 00 FF
```

#### 参数回读 (0xF4AA)：
```
FA 55 AF F4 AA 00 F6 [参数类型8字节] [预留238字节] CRC 00 FF
```

## 📊 状态码说明

### 反馈标志 (BackFlag)
- `0x01` - 操作成功
- `0x02` - 操作失败（通常是CRC校验错误）

### 升级状态
- `g_StartUpdateFirm = 1` - 升级模式已启动
- `g_UpdateSuccessful = 1` - 升级成功
- `g_UpdateFinishSuccessful = 1` - 升级完成
- `g_ucSystemResetFlag = 1` - 系统将复位

## 🔍 调试和监控

### 1. 全局变量监控

```c
extern uint8_t g_UpdateBackFlag;        // 升级反馈标志
extern uint8_t g_UpdateSuccessful;      // 升级成功标志
extern uint8_t g_ucSystemResetFlag;     // 系统复位标志
extern uint8_t g_UpdateFinishSuccessful; // 升级完成标志
extern uint8_t g_VersionQueryFlag;      // 版本查询标志
extern uint8_t g_StartUpdateFirm;       // 开始升级标志
extern Setpara_Data stSetPara;          // 参数数据结构
```

### 2. 参数访问示例

```c
// 读取当前参数
printf("当前频率: %d Hz\n", stSetPara.Setfre);
printf("当前波特率: %d\n", stSetPara.Setbaud);
printf("GNSS杆臂X: %.3f m\n", stSetPara.armX);

// 修改参数
stSetPara.Setfre = 100;  // 设置100Hz
stSetPara.armX = 1.5;    // 设置X轴杆臂1.5米

// 保存参数
SaveParaToFlash();
```

## ⚠️ 注意事项

### 1. 升级安全
- 升级过程中不要断电
- 确保升级文件完整性
- 升级失败会自动恢复

### 2. 参数有效性
- 参数设置前会进行有效性检查
- 无效参数会被拒绝
- 建议先查询当前参数再修改

### 3. Flash操作
- 参数保存需要擦除Flash扇区
- 频繁保存会影响Flash寿命
- 建议批量修改后统一保存

### 4. 系统复位
- 升级完成后系统会自动复位
- 某些参数修改需要复位生效
- 复位前确保重要数据已保存

## 🛠️ 故障排除

### 1. 协议不响应
- 检查帧头格式是否正确
- 检查CRC校验是否正确
- 检查命令码是否支持

### 2. 升级失败
- 检查升级文件格式
- 检查Flash空间是否足够
- 检查升级包序号是否连续

### 3. 参数设置失败
- 检查参数范围是否有效
- 检查Flash是否可写
- 检查系统是否处于正确状态

## 📚 相关文件

- `Protocol/SetParaBao.h` - 协议定义头文件
- `Protocol/SetParaBao.c` - 协议实现文件
- `Protocol/computerFrameParse.c` - 协议解析文件
- `PROTOCOL_MIGRATION_REPORT.md` - 移植报告

## 🎉 总结

新协议功能已完全集成到INS370M-25J20240919项目中，提供了：

- ✅ 完整的软件升级功能
- ✅ 详细的版本管理系统
- ✅ 全面的参数配置能力
- ✅ 可靠的数据校验机制
- ✅ 向后兼容的设计

使用时只需按照本指南的说明，即可享受与INS600-21A相同的协议功能！
