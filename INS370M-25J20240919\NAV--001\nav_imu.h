/***********************************************************************************
nav imu module header
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-5-17          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#ifndef __NAV_IMU_H__
#define __NAV_IMU_H__
#include "nav_type.h"

	
//函数声明
void Get_Param_Data(_NAV_Data_Full_t* NAV_Data_Full_temp,CombineDataTypeDef* CombineData_p);  //输入数据获取
void Get_IMU_Data(_NAV_Data_Full_t* NAV_Data_Full_temp,CombineDataTypeDef* CombineData_p); //传感器数据获取
void Load_Calib_Parms(_NAV_Data_Full_t* NAV_Data_Full_temp,CombineDataTypeDef* CombineData_p);   


#endif

