.\objects\bsp_exti.o: ..\bsp\src\bsp_exti.c
.\objects\bsp_exti.o: ..\bsp\inc\bsp_exti.h
.\objects\bsp_exti.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\bsp_exti.o: ..\Library\CMSIS\core_cm4.h
.\objects\bsp_exti.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdint.h
.\objects\bsp_exti.o: ..\Library\CMSIS\core_cmInstr.h
.\objects\bsp_exti.o: ..\Library\CMSIS\core_cmFunc.h
.\objects\bsp_exti.o: ..\Library\CMSIS\core_cm4_simd.h
.\objects\bsp_exti.o: ..\Library\CMSIS\GD\GD32F4xx\Include\system_gd32f4xx.h
.\objects\bsp_exti.o: ..\Source\inc\gd32f4xx_libopt.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rcu.h
.\objects\bsp_exti.o: ..\Library\CMSIS\GD\GD32F4xx\Include\gd32f4xx.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_adc.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_can.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_crc.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ctc.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dac.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dbg.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dci.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_dma.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exti.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fmc.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_fwdgt.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_gpio.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_syscfg.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_i2c.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_iref.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_pmu.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_rtc.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_sdio.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_spi.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_timer.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_trng.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_usart.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_wwdgt.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_misc.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_enet.h
.\objects\bsp_exti.o: D:\softwawe\Keil_v5\ARM\ARMCC\Bin\..\include\stdlib.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_exmc.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_ipa.h
.\objects\bsp_exti.o: ..\Library\GD32F4xx_standard_peripheral\Include\gd32f4xx_tli.h
