/*
 * Copyright (c) 2022-2028, INS Development Team
 *
 *
 * Change Logs:
 * Date           Author       Notes
 * 2023-06-04     Bill     	   the first version
 *
 */

#ifndef __GDTYPEDEFINE_H__
#define __GDTYPEDEFINE_H__
//#include "pcsimulator.h"
#include "bsp_sys.h"

#pragma pack(1)
//#define c_txmsgdatasize			8
//typedef struct _txmsgserialport {
//	unsigned char head0;
//	unsigned char head1;
//	short len;				//sizeof(txmsgserialport_t)
//	unsigned char cmd;
//	int parameter0;			//c_msg_ask_retransmission   0:-list index   1-retrans index
//	int parameter1;
//	int msglistindex;
//	int msgindex;
//	unsigned char data[c_txmsgdatasize];
//	unsigned char checksum;
//	unsigned char tail;
//} txmsgserialport_t;


//以下各项请参考FPGA, "惯导FMC通讯协议 V1.2.docx"文件说明
typedef struct gdwrxdatainfo {
    unsigned short  navi_test_info_counter;
    unsigned int	gnssInfo_gpsweek;
    unsigned int 	gnssInfo_gpssecond;
    unsigned char   navi_test_info_pps_en;
    unsigned char   navi_test_info_gps_valid;

    unsigned char   ppsDelay;
    
    float   navi_test_info_gyroX;
    float   navi_test_info_gyroY;
    float   navi_test_info_gyroZ;
    float   navi_test_info_sensor_temp;
    float   navi_test_info_accelX;
    float   navi_test_info_accelY;
    float   navi_test_info_accelZ;
    double  gnssInfo_Lon;
    double  gnssInfo_Lat;
    float   gnssInfo_Altitude;
    float   gnssInfo_ve;
    float   gnssInfo_vn;
    float   gnssInfo_vu;
    float   gnssInfo_Pitch;
    float   gnssInfo_Roll;
    float   gnssInfo_Heading;
    unsigned char   gnssInfo_PositioningState;
    unsigned int    gnssInfo_rtkStatus;
    unsigned char   gnssInfo_StarNum;
    unsigned int    canInfo_counter;
    float   canInfo_data_WheelSpeed_Front_Left;
    float   canInfo_data_WheelSpeed_Front_Right;
    float   canInfo_data_WheelSpeed_Back_Left;
    float   canInfo_data_WheelSpeed_Back_Right;
    float   canInfo_data_WheelSteer;
    float   canInfo_data_OdoPulse_1;
    float   canInfo_data_OdoPulse_2;
    float   canInfo_data_Gear;
    unsigned char Adj_Nav_Standard_flag;
    double  Adj_gnssAtt_from_vehicle2_0;
    double  Adj_gnssAtt_from_vehicle2_1;
    double  Adj_gnssAtt_from_vehicle2_2;
    double  Adj_acc_off_0;
    double  Adj_acc_off_1;
    double  Adj_acc_off_2;
    double  Adj_gyro_off_0;
    double  Adj_gyro_off_1;
    double  Adj_gyro_off_2;

    float   gnss_trackTrue;

    unsigned int    result_Nav_Status;
    unsigned char   result_Nav_Standard_flag;
    unsigned char   result_imuSelect;
    unsigned char   result_memsType;
    unsigned char   result_use_gps_flag;
    unsigned char   result_fusion_source;

    unsigned int	gnssInfo_headingStatus;
    unsigned int    gnssInfo_gpssecond982;
	float fog0;
} gdwrxdata_t;

typedef struct _gdalgrithomresult {
	double	longitude;	//算法结果，经纬高
	double	latitude;	//算法结果，经纬高
	float	altitude;	//算法结果，经纬高
	float	ve;		//算法结果，东向速度
	float	vn;		//算法结果，北向速度
	float	vu;		//算法结果，天向速度
	float	pitch;		//算法结果，俯仰角
	float	roll;		//算法结果，横滚角
	float	heading;	//算法结果，偏航角
} gdalgrithomresult_t;



typedef struct _gdwrxdatainfoTX {
    unsigned short head;
    unsigned short len;         //packet all length
    unsigned short type;        //data type  1: imu     2: cacv     3: pps      4:gnss      5:magnetometer  6:gd watch
    gdwrxdata_t  gdwdata;
    float tt;
    int packet;
    int packetT;
    unsigned short checksum;
} gdwrxdataTX_t;

//以下各项请参考FPGA, "惯导FMC通讯协议 V1.2.docx"文件说明
typedef struct gdwrxdata912info {
	u16		datalength;       		 //1 1	
	u16     selftestingcode;         //2 2
	u16     fpgaversion;             //3 3
	u16		watchversion;            //4 4
	u16     Xgears;                   //5 5
	float   Xflwheelspeed;            //6 6
	float   Xfrwheelspeed;            //7 8
	float   Xblwheelspeed;            //8 A
	float   Xbrwheelspeed;            //9 C
	u16     Xcaninfocounter;          //10 E

	s32     fogx;                    //11 F
	s32     fogy;                    //12 11
	s32     fogz;                    //13 13
//	float     fogx;                    //11 F
//	float     fogy;                    //12 11
//	float     fogz;                    //13 13
		
	s16     fogtemperaturex;         //14 15
	s16     fogtemperaturey;         //15 16
	s16     fogtemperaturez;         //16 17 
	float   accelerometerx;          //17
	float   accelerometery;          //18
	float   accelerometerz;          //19
	s16     accelerometertemp;       //20
	u16     reserve1e;               //21
	u16     reserve1f;               //22
	u16     Reserve20;               //23
	u16     gnssweek;                //24
	u32     millisecondofweek;       //25
					   
	u32     secondofweek;            //26
	u32     ppsdelay10ns;            //27
	u16     gpsstarnumber;           //28
	u16     rtkstatus;               //29
	u16     speedstatus;             //30
	u16     truenorthtrack[3];       //31
	float	  northvelocity;   		 //32
	float   eastvelocity;            //33
	float   upvelocity;              //34
	u16     positionstatus;          //35
	u16     directionoflat;          //36
	double  latitude;                //37
	u16     directionoflon;          //38
	double  longitude;               //39
	double  altitude;                //40
	u16     Headingstate;            //41
	u32     baselength;              //42
	float   roll;                    //43
	float   pitch;                   //44
	float   yaw;                     //45
	#if 0
	float   ECEF_X;                  //46
	float	ECEF_Y;                  //47
	float	ECEF_Z;                  //48
	float   geometricprecZ;          //49
	float	positionprecZ;           //50
	#else
	u16     gears;                   // 
	u16     caninfocounter;          //
	float   flwheelspeed;            // 
	float   frwheelspeed;            // 
	float   blwheelspeed;            // 
	float   brwheelspeed;            // 
	#endif
	float	timeprecisionZ;          //51
	float	verticalprecZ;           //52
	float	horizontalprecZ;         //53
	float	northprecisionZ;         //54
	float	eastprecisionZ;          //55
	float	endheightangleZ;         //56
	u16		checksum;                //57
	//u16		checksumA;               //58
	u16		frameindex;              //59
	
	//The following are the results of the algorithm
	double	Alongitude;	//算法结果，经纬高
	double	Alatitude;	//算法结果，经纬高
	float	Aaltitude;	//算法结果，经纬高
	float	Ave;		//算法结果，东向速度
	float	Avn;		//算法结果，北向速度
	float	Avu;		//算法结果，天向速度
	float	Apitch;		//算法结果，俯仰角
	float	Aroll;		//算法结果，横滚角
	float	Aheading;	//算法结果，偏航角
	u16		checksumA;               //58
} gdwrxdata912_t;  

typedef struct gdwrxdata912info_ex {
	double     r_Gyro[3];                    
	double     Acc[3];                    
}gdwrxdata912_t_ex;

typedef struct
{
	unsigned short	head1;			//帧头BB00
	unsigned short	head2;			//帧头DBBD
	unsigned short  dataLen;		//后面数据包长度，不包含帧头、本字节和校验
    gdwrxdata912_t fpgaPreDodata;			//FPGA原始数据包-预处理后数据
  gdwrxdata912_t_ex fpgaPreDodata_ex;
    unsigned int fpgaItrCount;		//FPGA中断计数
    unsigned int fpgaLoopCount;		//FPGA处理循环计数
	unsigned short Status;			//当前处理状态
	unsigned short CheckSum;		//检验
} FpgadataPreDoSend_t;


typedef struct gdwrxdatainfo912TX {	//GD watch data...   .gdw 文件格式数据的输出
    unsigned short head;
    unsigned short len;         //packet all length
    unsigned short type;        //data type  1: imu     2: cacv     3: pps      4:gnss      5:magnetometer  6:gd watch		
    gdwrxdata912_t  gdwdata;
    float tt;
    int packet;
    int packetT;
    unsigned short checksum;
} gdwrxdata912TX_t;


#define	c_msg_ask_scha63xcacv		0x12	//上传村田IMU的校准参数，芯驰方案和Infineon方案使用
#define	c_msg_ask_retransmission	0x26	//表示信息帧为上位机反馈回的丢帧信息
#define	c_msg_ask_driversettings	0x39	//上传给上位机的驱动设置信息

#define GD_DRIVERSDATA_MAXCOUNT     200		//最近发送信息缓冲区大小
#define DRIVERSDATATYPE_GNSS        1		//GNSS信息
#define DRIVERSDATATYPE_IMU         2		//IMU信息
#define DRIVERSDATATYPE_CAN         3		//CAN信息
#define DRIVERSDATATYPE_PPS         4		//PPS信息
#define DRIVERSDATATYPE_GDW         6		//GD方案观测量信息
#define DRIVERSDATATYPE_GDW912      9		//GD INS912方案观测量信息
#define DRIVERSDATATYPE_FPGA912	    10		//INS912 FPGA传给GD的数据

typedef struct _driversdatatosimulator {
    int driversdatatype;    //0: null   1:-gnss   2-imu   	  3-can     	4-pps   	5-      6-gd watch
							//7: driver settings  8-imu data  9-912 watchD
    int driverspacket;
    union {
        //rxonecaninfo_t  can;
        //imuorgdatasendtopc_t    imu;
        //gnessrxdataTX_t gnss;
        //gnessppsTX_t    pps;
        gdwrxdata912TX_t   gdw;
    } data;
} driversdatatosimulator_t;

//驱动数据缓冲区
typedef struct _driversdatatosimulatorlist {
    int st;
    int size;
    driversdatatosimulator_t    driversdata[GD_DRIVERSDATA_MAXCOUNT];
} driversdatatosimulatorlist_t;


#define c_txmsgdatasize			8
typedef struct _txmsgserialport {
	unsigned char head0;
	unsigned char head1;
	short len;				//sizeof(txmsgserialport_t)
	unsigned char cmd;
	int parameter0;			//c_msg_ask_retransmission   0:-list index   1-retrans index
	int parameter1;
	int msglistindex;
	int msgindex;
	unsigned char data[c_txmsgdatasize];	
	unsigned char checksum;
	unsigned char tail;
} txmsgserialport_t;


typedef struct _driversettings {
	char projectname[16];	//项目名称
	char mcutype;			//mcu主控型号
	char datatype;			//数据类型
	int imuframe;		//imu数据大小
	int gnssframe;		//gnss数据大小
	int canframe;		//can数据大小
	int ppsframe;		//pps数据大小
	int gdwframe;		//GD方案观测量数据大小
	int magnetframe;	//磁力计数据大小
	int pressureframe;	//气压计数据大小
	int opticalgryoframe;		//单个光纤数据大小
	int imu2600hzframe;			//imu 2600Hz数据大小
	int ins912fpgaframe;		//INS912 FPGA数据大小
	int turntableaccgyroframe;	//转台上测试加计和陀螺数据大小
	int gdwframe912;			//INS912观测量数据大小
	int reserved[16];			//保留
} driversettings_t;

typedef struct _driversettingstopc {
	unsigned short head;		//协议头 0xaa55
	unsigned short len;         //packet all length
	unsigned short type;    	//data type  1: imu     2: cacv		6: GD watch		7: driversettings
	driversettings_t settings;	//驱动设置
	unsigned short checksum;	//数据校验值
} driversettingstopc_t;


//---Track-AllFpgaDatas-----------------------------------------------------------------------------------
#if defined(CMPL_CODE_EDWOY)
//2024.6.28
//以下各项请参考FPGA, "惯导FMC通讯协议 V1.2.docx"文件说明
typedef struct _ins370info {
	u16		  datalength;       		 //1 
	u16     selftestingcode;         //2 
	u16     fpgaversion;             //3 
	u16		  watchversion;            //4 
	u16     Xgears;                   //5 
	float   Xflwheelspeed;            //6 
	float   Xfrwheelspeed;            //7 
	float   Xblwheelspeed;            //8 
	float   Xbrwheelspeed;            //9 
	u16     Xcaninfocounter;          //10
  
	float     fogx;                    //11
	float     fogy;                    //12
	float     fogz;                    //13

	s16     fogtemperaturex;         //14
	s16     fogtemperaturey;         //15
	s16     fogtemperaturez;         //26
	float   accelerometerx;          //17
	float   accelerometery;          //18
	float   accelerometerz;          //19
	s16     accelerometertemp;       //20
	u16     reserve1e;               //21
	u16     reserve1f;               //22
	u16     Reserve20;               //23
	u16     gnssweek;                //24
	u32     millisecondofweek;       //25
					   
	u32     secondofweek;            //26
	u32     ppsdelay10ns;            //27
	u16     gpsstarnumber;           //28
	u16     rtkstatus;               //29
	u16     speedstatus;             //30
	u16     truenorthtrack[3];       //31
	float	  northvelocity;   		 //32
	float   eastvelocity;            //33
	float   upvelocity;              //34
	u16     positionstatus;          //35
	u16     directionoflat;          //36
	double  latitude;                //37
	u16     directionoflon;          //38
	double  longitude;               //39
	double  altitude;                //40
	u16     Headingstate;            //41
	u32     baselength;              //42
	float   roll;                    //43
	float   pitch;                   //44
	float   yaw;                     //45
#if 0
	float   ECEF_X;                  //46
	float	ECEF_Y;                  //47
	float	ECEF_Z;                  //48
	float   geometricprecZ;          //49
	float	positionprecZ;           //50
#else
	u16     gears;                   // 
	u16     caninfocounter;          //
	float   flwheelspeed;            // 
	float   frwheelspeed;            // 
	float   blwheelspeed;            // 
	float   brwheelspeed;            // 
#endif
	float	timeprecisionZ;          //51
	float	verticalprecZ;           //52
	float	horizontalprecZ;         //53
	float	northprecisionZ;         //54
	float	eastprecisionZ;          //55
	float	endheightangleZ;         //56
	u16		checksum;                //57
	//u16		checksumA;               //58
	u16		frameindex;              //59
	
	//The following are the results of the algorithm
	double	Alongitude;	//算法结果，经纬高
	double	Alatitude;	//算法结果，经纬高
	float	Aaltitude;	//算法结果，经纬高
	float	Ave;		//算法结果，东向速度
	float	Avn;		//算法结果，北向速度
	float	Avu;		//算法结果，天向速度
	float	Apitch;		//算法结果，俯仰角
	float	Aroll;		//算法结果，横滚角
	float	Aheading;	//算法结果，偏航角
	u16		checksumA;               //58
} sttIns370Trk_t;  //size = 246bytes

#endif 
//---End Track-AllFpgaDatas-----------------------------------------------------------------------------------*/


#pragma pack()

extern  int gdriverspacket;
extern  int ggdworgdata_packet;
extern  gdwrxdata912TX_t	gtmpgdrx;
extern	gdalgrithomresult_t	galgrithomresultTx;
extern  driversdatatosimulatorlist_t	gdriverdatalist;
extern  driversettingstopc_t	gdriversettings;
extern	float gfog0;



extern	int mcusendtopcdriversdata(int cmd, int listindex, int driverindex);
extern	int initializationdriversettings(void);


#endif  //__GDTYPEDEFINE_H__
