//---------------------------------------------------------
// Copyright (c) 2024,INAV All rights reserved.
//
// 文件名称：SetParaBao.c
// 文件标识：
// 文件摘要：参数设置和软件升级协议实现
//
// 当前版本：V1.0
// 作    者：zhangjianzhou
// 完成时间：2024.11.20
// 移植适配：INS370M-25J20240919项目
//---------------------------------------------------------

#include <stdio.h>
#include <string.h>
#include "gd32f4xx.h"
#include "computerFrameParse.h"
#include "SetParaBao.h"
#include "UartAdapter.h"
#include "systick.h"
#include "INS_Data.h"
#include "bsp_flash.h"

// 全局变量定义
uint8_t	g_UpdateBackFlag=1;         // 反馈标志(0x01-正常/02-异常)
uint8_t g_UpdateSuccessful=0;        // 升级成功标志
uint8_t g_ucSystemResetFlag=0;       // 系统复位标志
uint8_t g_UpdateFinishSuccessful=0;  // 升级完成是否成功
uint8_t g_VersionQueryFlag = 0;      // 版本查询标志
uint8_t g_StartUpdateFirm = 0;       // 开始升级固件标志

Setpara_Data stSetPara={0};          // 参数设置数据结构

// 外部函数声明
extern void delay_ms(uint32_t ms);
extern void uart4sendmsg(char* msg, uint16_t len);

// CRC校验函数实现
uint8_t crc_verify_8bit(uint8_t *data, uint16_t length)
{
    uint8_t crc = 0;
    uint16_t i, j;

    for(i = 0; i < length; i++)
    {
        crc ^= data[i];
        for(j = 0; j < 8; j++)
        {
            if(crc & 0x80)
                crc = (crc << 1) ^ 0x07;
            else
                crc <<= 1;
        }
    }
    return crc;
}

// 通用帧头设置
void SendPara_SetHead(p_parabag_Other_back stPara, uint16_t Type, uint16_t Len)
{
    stPara->head.header[0] = SETPARA_HEADER_0;
    stPara->head.header[1] = SETPARA_HEADER_1;
    stPara->head.header[2] = SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

// 通用帧尾设置
void SendPara_SetEnd(p_parabag_Other_back stPara, uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_Other_back)]={0};
    
    memcpy(Databuf, stPara, Len-3);
    
    stPara->ender.check = crc_verify_8bit(&Databuf[3], Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

// 升级开始帧头设置
void UpdateStart_SetHead(p_parabag_UpdateStart_back stPara, uint16_t Type, uint16_t Len)
{
    stPara->head.header[0] = SETPARA_HEADER_0;
    stPara->head.header[1] = SETPARA_HEADER_1;
    stPara->head.header[2] = SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

// 升级开始帧尾设置
void UpdateStart_SetEnd(p_parabag_UpdateStart_back stPara, uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_UpdateStart_back)]={0};
    
    memcpy(Databuf, stPara, Len-3);
    
    stPara->ender.check = crc_verify_8bit(&Databuf[3], Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

// 升级发送帧头设置
void UpdateSend_SetHead(p_parabag_UpdateSend_back stPara, uint16_t Type, uint16_t Len)
{
    stPara->head.header[0] = SETPARA_HEADER_0;
    stPara->head.header[1] = SETPARA_HEADER_1;
    stPara->head.header[2] = SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

// 升级发送帧尾设置
void UpdateSend_SetEnd(p_parabag_UpdateSend_back stPara, uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_UpdateSend_back)]={0};
    
    memcpy(Databuf, stPara, Len-3);
    
    stPara->ender.check = crc_verify_8bit(&Databuf[3], Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

// 升级完成帧头设置
void UpdateEnd_SetHead(p_parabag_UpdateEnd_back stPara, uint16_t Type, uint16_t Len)
{
    stPara->head.header[0] = SETPARA_HEADER_0;
    stPara->head.header[1] = SETPARA_HEADER_1;
    stPara->head.header[2] = SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

// 升级完成帧尾设置
void UpdateEnd_SetEnd(p_parabag_UpdateEnd_back stPara, uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_UpdateEnd_back)]={0};
    
    memcpy(Databuf, stPara, Len-3);
    
    stPara->ender.check = crc_verify_8bit(&Databuf[3], Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

// 升级终止帧头设置
void UpdateStop_SetHead(p_parabag_UpdateStop_back stPara, uint16_t Type, uint16_t Len)
{
    stPara->head.header[0] = SETPARA_HEADER_0;
    stPara->head.header[1] = SETPARA_HEADER_1;
    stPara->head.header[2] = SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

// 升级终止帧尾设置
void UpdateStop_SetEnd(p_parabag_UpdateStop_back stPara, uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_UpdateStop_back)]={0};
    
    memcpy(Databuf, stPara, Len-3);
    
    stPara->ender.check = crc_verify_8bit(&Databuf[3], Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

// 参数回读帧头设置
void ReadPara_SetHead(p_parabag_ReadPara_back stPara, uint16_t Type, uint16_t Len)
{
    stPara->head.header[0] = SETPARA_HEADER_0;
    stPara->head.header[1] = SETPARA_HEADER_1;
    stPara->head.header[2] = SETPARA_HEADER_2;
    stPara->head.Type = Type;
    stPara->head.len = Len;
}

// 参数回读帧尾设置
void ReadPara_SetEnd(p_parabag_ReadPara_back stPara, uint16_t Len)
{
    uint8_t Databuf[sizeof(parabag_ReadPara_back)]={0};
    
    memcpy(Databuf, stPara, Len-3);
    
    stPara->ender.check = crc_verify_8bit(&Databuf[3], Len - 3 - 3);
    stPara->ender.ender[0] = SETPARA_END_0;
    stPara->ender.ender[1] = SETPARA_END_1;
}

// 从Flash读取参数
void ReadParaFromFlash(void)
{
    uint8_t ReadFlashDatabuf[sizeof(Setpara_Data)]={0};

    // 读取Flash中的参数
    ReadFlashByAddr(SAVE_SET_PARA_ADDR, ReadFlashDatabuf, sizeof(Setpara_Data));

    memcpy(&stSetPara, ReadFlashDatabuf, sizeof(Setpara_Data));

    // 检查参数有效性，如果无效则设置默认值
    if(stSetPara.Flag != 422)
    {
        memset(&stSetPara, 0, sizeof(Setpara_Data));
        stSetPara.Flag = 422;
        stSetPara.Setbaud = 4608;  // 默认波特率460800
        stSetPara.Setfre = SETPARA_DATAOUT_FPGA_FREQ;  // 默认频率

        memcpy(ReadFlashDatabuf, &stSetPara, sizeof(Setpara_Data));

        // 保存默认参数到Flash
        InitFlashAddr(SAVE_SET_PARA_ADDR - ADDR_FMC_SECTOR_6);
        WriteFlash(ReadFlashDatabuf, sizeof(Setpara_Data));
        EndWrite();
    }

    InitParaToAlgorithm();
}

// 将参数初始化到算法模块
void InitParaToAlgorithm(void)
{
    // 将参数同步到算法模块
    // 这里需要根据具体的算法接口进行实现
    
    // 示例：设置频率
    if(stSetPara.Setfre > 0)
    {
        hSetting.settingData.freq = stSetPara.Setfre;
    }
    
    // 示例：设置GNSS杆臂参数
    hSetting.settingData.param.gnssArmLength[0] = stSetPara.armX;
    hSetting.settingData.param.gnssArmLength[1] = stSetPara.armY;
    hSetting.settingData.param.gnssArmLength[2] = stSetPara.armZ;
    
    // 示例：设置天线安装角度
    hSetting.settingData.param.gnssAtt_from_vehicle[0] = stSetPara.angleX;
    hSetting.settingData.param.gnssAtt_from_vehicle[1] = stSetPara.angleY;
    hSetting.settingData.param.gnssAtt_from_vehicle[2] = stSetPara.angleZ;
    
    // 示例：设置车体杆臂参数
    hSetting.settingData.param.OBArmLength[0] = stSetPara.vectorX;
    hSetting.settingData.param.OBArmLength[1] = stSetPara.vectorY;
    hSetting.settingData.param.OBArmLength[2] = stSetPara.vectorZ;
    
    // 示例：设置安装角度偏差
    hSetting.settingData.param.OBAtt_from_vehicle[0] = stSetPara.pitch;
    hSetting.settingData.param.OBAtt_from_vehicle[1] = stSetPara.roll;
    hSetting.settingData.param.OBAtt_from_vehicle[2] = stSetPara.Course;
}

// 保存参数到Flash
void SaveParaToFlash(void)
{
    uint8_t WriteFlashDatabuf[sizeof(Setpara_Data)]={0};

    memcpy(WriteFlashDatabuf, &stSetPara, sizeof(Setpara_Data));

    // 使用项目中的Flash操作函数
    InitFlashAddr(SAVE_SET_PARA_ADDR - ADDR_FMC_SECTOR_6);
    WriteFlash(WriteFlashDatabuf, sizeof(Setpara_Data));
    EndWrite();
}

// 初始化参数设置模块
void SetParaBao_Init(void)
{
    // 从Flash读取参数
    ReadParaFromFlash();

    // 初始化升级相关标志
    g_UpdateBackFlag = 1;
    g_UpdateSuccessful = 0;
    g_ucSystemResetFlag = 0;
    g_UpdateFinishSuccessful = 0;
    g_VersionQueryFlag = 0;
    g_StartUpdateFirm = 0;
}

// 设置波特率
void SetParaBaud(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_Setbaud stBaud;
    memcpy(&stBaud, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stBaud.ender.check)
    {
        stSetPara.Setbaud = stBaud.info.SetbaudPara;
        stSendPara.info.BackFlag = 0x01;
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    SendPara_SetHead(&stSendPara, SETPARA_TYPE1_baud, BaoLen);
    SendPara_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);

    delay_ms(500);
    // 重新配置UART波特率
    // 这里需要根据具体的UART配置函数进行实现
}

// 设置数据输出频率
void SetParaFrequency(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_Setfrequency stfrequency;
    memcpy(&stfrequency, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);
    if(crc == stfrequency.ender.check)
    {
        stSetPara.Setfre = stfrequency.info.SetfrePara;
        hSetting.settingData.freq = stSetPara.Setfre;
        stSendPara.info.BackFlag = 0x01;
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    SendPara_SetHead(&stSendPara, SETPARA_TYPE1_frequency, BaoLen);
    SendPara_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
}

// 软件升级开始命令
void SetParaUpdateStart(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateStart_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_UpdateStart_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateStart_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateStart_back));

    parabag_SetType stUpdateStart;
    memcpy(&stUpdateStart, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);

    if(crc == stUpdateStart.ender.check)
    {
        stSendPara.info.BackFlag = 0x01;

        stSendPara.info.Vermain = VERMAIN;
        stSendPara.info.Verminor = VERMINOR;
        stSendPara.info.Revision = REVISION;
        stSendPara.info.VerDate = VERYEAR*10000 + VERMONTH*100 + VERDAY;
        stSendPara.info.Plan = PLAN;
        stSendPara.info.Suffix = SUFFIX;
        stSendPara.info.Custom = CUSTOM;

        g_StartUpdateFirm = 1;
        g_UpdateSuccessful = 0;
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    g_VersionQueryFlag = 1; // 版本查询

    stSendPara.info.Count++; // 帧计数

    delay_ms(100);

    UpdateStart_SetHead(&stSendPara, SETPARA_TYPE1_UPDATE_START, BaoLen);
    UpdateStart_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
    delay_ms(100);
}

// 发送升级包命令
void SetParaUpdateSend(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateSend_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_UpdateSend_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateSend_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateSend_back));
    g_UpdateBackFlag = 1;
    g_VersionQueryFlag = 0; // 版本查询

    parabag_UpdateSend stUpdateSend;
    memcpy(&stUpdateSend, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);

    if(crc == stUpdateSend.ender.check)
    {
        // 处理升级数据包
        // 这里需要实现具体的Flash写入逻辑
        stSendPara.info.BackFlag = 0x01;

        // 写入升级数据到Flash
        // uint32_t flash_addr = APP_LOADED_ADDR + stUpdateSend.info.BaoIndex * 128;
        // fmc_write_8bit(stUpdateSend.info.UpdateData, flash_addr, stUpdateSend.info.Length);
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    stSendPara.info.Count = stUpdateSend.info.Count;

    UpdateSend_SetHead(&stSendPara, SETPARA_TYPE1_UPDATE_SEND, BaoLen);
    UpdateSend_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
}

// 升级包完成命令
void SetParaUpdateEnd(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateEnd_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_UpdateEnd_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateEnd_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateEnd_back));

    parabag_SetType stUpdateEnd;
    memcpy(&stUpdateEnd, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);

    if(crc == stUpdateEnd.ender.check)
    {
        stSendPara.info.BackFlag = 0x01;
        g_UpdateFinishSuccessful = 1;
        g_ucSystemResetFlag = 1; // 设置系统复位标志
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    UpdateEnd_SetHead(&stSendPara, SETPARA_TYPE1_UPDATE_END, BaoLen);
    UpdateEnd_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);

    delay_ms(100);

    // 如果升级成功，延时后复位系统
    if(g_UpdateFinishSuccessful)
    {
        delay_ms(1000);
        NVIC_SystemReset(); // 系统复位
    }
}

// 升级终止命令
void SetParaUpdateStop(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateStop_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_UpdateStop_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateStop_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateStop_back));

    parabag_SetType stUpdateStop;
    memcpy(&stUpdateStop, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);

    if(crc == stUpdateStop.ender.check)
    {
        stSendPara.info.BackFlag = 0x01;
        g_StartUpdateFirm = 0; // 停止升级
        g_UpdateSuccessful = 0;
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    UpdateStop_SetHead(&stSendPara, SETPARA_TYPE1_UPDATE_STOP, BaoLen);
    UpdateStop_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
}

// 版本查询命令
void SetParaReadVersion(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_UpdateStart_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_UpdateStart_back);
    uint8_t SendDatabuf[sizeof(parabag_UpdateStart_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_UpdateStart_back));

    parabag_SetType stReadVer;
    memcpy(&stReadVer, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);

    if(crc == stReadVer.ender.check)
    {
        stSendPara.info.BackFlag = 0x01;

        stSendPara.info.Vermain = VERMAIN;
        stSendPara.info.Verminor = VERMINOR;
        stSendPara.info.Revision = REVISION;
        stSendPara.info.VerDate = VERYEAR*10000 + VERMONTH*100 + VERDAY;
        stSendPara.info.Plan = PLAN;
        stSendPara.info.Suffix = SUFFIX;
        stSendPara.info.Custom = CUSTOM;
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    stSendPara.info.Count++;

    UpdateStart_SetHead(&stSendPara, SETPARA_TYPE1_readver, BaoLen);
    UpdateStart_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
}

// 参数回读命令
void SetParaReadPara(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_ReadPara_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_ReadPara_back);
    uint8_t SendDatabuf[sizeof(parabag_ReadPara_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_ReadPara_back));

    parabag_SetType stReadPara;
    memcpy(&stReadPara, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);

    if(crc == stReadPara.ender.check)
    {
        stSendPara.info.BackFlag = 0x01;
        // 这里可以添加具体的参数回读逻辑
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    ReadPara_SetHead(&stSendPara, SETPARA_TYPE1_readpara, BaoLen);
    ReadPara_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
}

// 固化参数命令
void SetParaSolidify(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetType stSolidify;
    memcpy(&stSolidify, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);

    if(crc == stSolidify.ender.check)
    {
        // 保存参数到Flash
        SaveParaToFlash();
        stSendPara.info.BackFlag = 0x01;
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    SendPara_SetHead(&stSendPara, SETPARA_TYPE1_solidify, BaoLen);
    SendPara_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
}

// 恢复出厂设置命令
void SetParaFactory(p_dmauart_t pdmauart)
{
    uint8_t crc=0;
    parabag_Other_back stSendPara;
    uint16_t BaoLen = sizeof(parabag_Other_back);
    uint8_t SendDatabuf[sizeof(parabag_Other_back)]={0};
    memset(&stSendPara, 0, sizeof(parabag_Other_back));

    parabag_SetType stFactory;
    memcpy(&stFactory, pdmauart->rxbuffer, SETPARA_RXBUFFER_DMA_SIZE);

    crc = crc_verify_8bit(&pdmauart->rxbuffer[3], SETPARA_RXBUFFER_DMA_SIZE - 3 - 3);

    if(crc == stFactory.ender.check)
    {
        // 恢复出厂设置
        memset(&stSetPara, 0, sizeof(Setpara_Data));
        stSetPara.Flag = 422;
        stSetPara.Setbaud = 4608;  // 默认波特率460800
        stSetPara.Setfre = SETPARA_DATAOUT_FPGA_FREQ;  // 默认频率

        SaveParaToFlash();
        InitParaToAlgorithm();

        stSendPara.info.BackFlag = 0x01;
    }
    else
    {
        stSendPara.info.BackFlag = 0x02;
    }

    SendPara_SetHead(&stSendPara, SETPARA_TYPE1_factory, BaoLen);
    SendPara_SetEnd(&stSendPara, BaoLen);

    memcpy(SendDatabuf, &stSendPara, BaoLen);
    uart4sendmsg((char*)SendDatabuf, BaoLen);
}
