/*!
    \file  fpga.c
    \brief 
*/


#include "fpgad.h"
#include "deviceconfig.h"
#include "ins.h"
//#include "INS_init.h"
#include "appmain.h"


fpgadata_t		gpagedata;															//全局数据结构体，存放当前FPGA一帧数据
CanDataTypeDef	gcanInfo;
FpgadataSend_t gfpgadataSend;														//发送FPGA原始数据
//

unsigned short gfpgadata[200];
char infoArr[32];
//int gberror = 0;
//unsigned short gpsssecond0 = 0;
//unsigned short gpsssecond1 = 0;
int	gfpgasenddatalen = 0;
unsigned int gframeindex = 0;
unsigned int fpga_syn_btw = 0;		//FPGA硬件帧、循环帧  同步数据差异
unsigned int fpga_syn_btw_last = 0;		//FPGA硬件帧、循环帧  同步数据差异,最后缓存计数
unsigned int fpga_syn_NAVI_btw = 0;		//FPGA硬件帧、算法帧  同步数据差异
//#define OUT_GETFPGADATA_DEBUG_INFO	0
//#define FACTOR_FOG_GYRO           	300000.0

//#ifdef CMPL_CODE_EDWOY
//char r_testString[48] = {0};
//#endif 

extern char	gins912data_cash[600];


//***************************************************************************************************************************


void test_gyc_FPGATo422(void)
	//测试：//将FPGA数据直接通过422发送出去，20240709
{
	memcpy(&gins912data_cash, &gpagedata, sizeof(gfpgadata));		//
	uart4sendmsg(gins912data_cash, sizeof(gfpgadata));
}

void get_fpgadata_do(void)
//将FPGA数据从FMC总线读入gfpgadata，然后赋值给结构体gpagedata
{
	int	i;
	gfpgasenddatalen = *(unsigned short*)(0x60000000 + (0 << 1));
	if (gfpgasenddatalen >= 1024)	return; 

	unsigned short	checksum = 0;
	for (i = 0; i < gfpgasenddatalen; i++) {											//从FPGA接受20 Word, 并转换为40个字符
		gfpgadata[i] = *(unsigned short*)(0x60000000 + (i << 1));		//从FPGA读取16bit数据		
	}

	checksum = 0;
	for (i = 0; i < gfpgasenddatalen; i++) //再计算校验码
	{
		checksum += gfpgadata[i];
	}

	gfpgadata[i] = checksum;
	gfpgadata[i + 1] = gframeindex++;
	
	gpagedata = *(fpgadata_t*)&gfpgadata;
	
#ifdef test_gyc_FPGATo422_20240709
	test_gyc_FPGATo422();
#endif		
	
	return;	
}

void get_fpgadata_setADLX355MemsData(void)
	//将ADLX355MEMS数据压入gpagedata中
{
//	gfpgadata[5]  = adlxdata[0];//再填mems
//	gfpgadata[6]  = adlxdata[1];
//	gfpgadata[7]  = adlxdata[2];
//	gfpgadata[8]  = adlxdata[3];
//	gfpgadata[9]  = adlxdata[4];
//	gfpgadata[10] = adlxdata[5];
	gfpgadata[23]  = adlxdata[0];//再填mems
	gfpgadata[24]  = adlxdata[1];
	gfpgadata[25]  = adlxdata[2];
	gfpgadata[26]  = adlxdata[3];
	gfpgadata[27]  = adlxdata[4];
	gfpgadata[28]  = adlxdata[5];	
	
	int	i;
	unsigned short	checksum = 0;
	checksum = 0;
	for (i = 0; i < gfpgasenddatalen; i++) //再计算校验码
	{
		checksum += gfpgadata[i];
	}
	
	gfpgadata[i] = checksum;
	gfpgadata[i + 1] = gframeindex++;
	
	gpagedata = *(fpgadata_t*)&gfpgadata;

}

void get_fpgadata_after_otherDataDo(void)
	//fpgadata其它数据处理
{
	#ifdef DEVICE_ACC_TYPE_ADLX355 
			get_fpgadata_setADLX355MemsData();	//将ADLX355MEMS数据压入gpagedata中
	#endif
	
	//在以下加入其它型号模块处理代码函数入口...
	
	
	
}

void get_fpgadata_before_ADXL355_sendcmd()
	//ADXL355发送命令，在ADXL355模块没有跑起来之前，可以间隔5MS重复发送命令
{
		if(false==adxl355_is_running)
		{
		  uart7sendmsg2(ADXL355_CMD, sizeof(ADXL355_CMD));

			delay_ms(5);
		}
}

void get_fpgadata_before()
	//每次获取FPGA数据之前需要处理的事宜
{
	#ifdef DEVICE_ACC_TYPE_ADLX355 
			get_fpgadata_before_ADXL355_sendcmd();
	#endif
	
	//在以下加入其它型号模块处理代码函数入口...
	

	
}

void fpgadata_syn_count_do(void)
	//FPGA帧数据信号同步处理
//可以将 循环帧计数fpga_loop_count、及 硬触发中断计数fpga_syn_count 发送到 上位机
{
	fpga_loop_count++;
	if (fpga_loop_count>3906250000)			//3906250000=250*250*250*250，每秒200次，约计数226天
	{
		fpga_loop_count =0;	//重新计数
		fpga_syn_count  =0;
	}
	if (fpga_syn_count>fpga_loop_count)
	{
		fpga_syn_btw = (fpga_syn_count - fpga_loop_count) % 0xFFFF;		
		//发送一次差异反馈信息
		if(fpga_syn_btw_last!=fpga_syn_btw)
		{
			fpga_syn_btw_last = fpga_syn_btw;				
			
			strcpy(infoArr, "ERRfs    fpga_syn_count erro\r\n");    
			infoArr[5]=(fpga_syn_btw_last / 0x100) % 0xFF;
			infoArr[6]=fpga_syn_btw_last % 0xFF;
			uart4sendmsg(infoArr, strlen(infoArr));
		}
	}		
	//
	fpga_syn_NAVI_btw = fpga_syn_count - NaviCompute_do_count;	
}

void get_fpgadata(void)
//获取FPGA数据
{
				
	fpgadata_syn_count_do();
	
	get_fpgadata_before();							//每次获取FPGA数据之前需要处理的事宜
	
	get_fpgadata_do();								//实际获取FPGA数据
	
	get_fpgadata_after_otherDataDo();				//fpgadata其它数据处理	
}

