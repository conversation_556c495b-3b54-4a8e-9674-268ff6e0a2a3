#ifndef ____INS_DATA_HX____
#define ____INS_DATA_HX____

#include "gd32f4xx.h"
#include "arm_math.h"
#include "gnss.h"
#include "can_data.h"
#include "imu_data.h"
#include "INS_sys.h"
#include "tlhtype.h"
#include "INS912AlgorithmEntry.h"
//#include "nav_type.h"

//#define FIRST_PROGRAM_BYTE		0xCAAC

#define PRODUCT_ID			0x1A0C;
#define DEVICE_ID			0x3E01;

typedef enum LED_State_t{
	LED_STATE_POWER_OFF = 0,
	LED_STATE_WHEEL_OK_INIT_OK = 1,
	LED_STATE_WHEEL_OK_INIT_ERR = 2,
	LED_STATE_WHEEL_ERR_INIT_OK = 3,
	LED_STATE_WHEEL_ERR_INIT_ERR = 4,
}LEDStateEnumTypeDef;


typedef struct INS_data_t 
{
	GPSDataTypeDef GNSSS_Data;
	IMUDataTypeDef IMU_Data;
	CcanDataTypeDef CAN_Data;
	uint8_t IMU_DataRead;
	uint8_t GNSS_DataRead;
	uint8_t updated;
} INSDataTypeDef;

extern INSDataTypeDef hINSData;
extern LEDStateEnumTypeDef g_LEDIndicatorState;


//typedef union rs422_frame_define
//{
//	struct
//	{
//		uint8_t 			header[3];	//0xbd,0xdb,0x0b
//		short 			roll;		//横滚角
//		short 			pitch;		//俯仰角
//		short			azimuth;	//方位角
//		short 			gyroX;		//陀螺x轴
//		short 			gyroY;		//陀螺y轴
//		short			gyroZ;		//陀螺z轴
//		short 			accelX;		//加表x轴
//		short 			accelY;		//加表y轴
//		short			accelZ;		//加表z轴
//		long			latitude;	//纬度
//		long			longitude;	//经度
//		long			altitude;	//高度
//		short			speed_N;	//北向速度
//		short			speed_E;	//东向速度
//		short			speed_G;	//地向速度
//		uint8_t			status;
//		uint8_t			reserved[6];
//		struct poll_data	poll_frame;
//		uint8_t				xor_verify1;
//		uint32_t			gps_week;
//		uint8_t				xor_verify2;
//	} data_stream;
//	uint8_t fpga_cache[100];
//} FPGA_FRAME_DEF;

//#pragma pack(1)
//typedef __packed struct  poll_data
//{
//	uint16_t	data1;
//	uint16_t 	data2;
//	uint16_t	data3;
//	//uint32_t	gps_time;
//	//uint8_t	type;
//} POLL_DATA, *pPOLL_DATA;


//typedef struct{
//		uint8_t 			header[3];	//0xbd,0xdb,0x0b
//		short 			roll;		//横滚角
//		short 			pitch;		//俯仰角
//		short			azimuth;	//方位角
//		short 			gyroX;		//陀螺x轴
//		short 			gyroY;		//陀螺y轴
//		long			gyroZ;		//陀螺z轴
//		short 			accelX;		//加表x轴
//		short 			accelY;		//加表y轴
//		short			accelZ;		//加表z轴
//		long			latitude;	//纬度
//		long			longitude;	//经度
//		long			altitude;	//高度
//	    short				ve;			//东向速度
//	    short				vn;			//北向速度
//	    short				vu;			//天向速度

//		uint8_t			status;
//		uint8_t			reserved[6];
//	    POLL_DATA			poll_frame;
//	    uint32_t			gps_time;
//	    uint8_t				type;
//		uint8_t				xor_verify1;
//		uint32_t			gps_week;
//		uint8_t				xor_verify2;
//	    uint8_t				GpsFlag_Pos;
//	    uint8_t				NumSV;
//	    uint8_t 			         GpsFlag_heading;
//	    uint8_t                            Gps_Age;
//	    uint8_t                    	Car_Status;
//	    uint8_t				INS_status;

//	   short 				std_lat;
//	   short 				std_lon;
//	   short				std_height;
//	   short				std_heading;
//		
//	   uint32_t			gpssecond;
//	   //
//	   uint8_t				Nav_Standard_flag;  //0:未标定 1：标定中 2：标定完成
//	   /*0：NormalSts
//		1：FaltSts
//		2：Reserved1
//		3:Reserved2
//		4:Reserved3
//		5:Reserved4
//		6:Reserved5
//		7:Reserved6*/
//		uint8_t				 INS_States;
//		uint8_t 				Gear;/* 汽车档位 */

//		short   	WheelSpeed;

//} DATA_STREAMx;


typedef  union rs422_frame_t
{
    DATA_STREAMx data_stream;
	uint8_t fpga_cache[100];
} FPGA_FRAME_DEF;
#pragma pack()


typedef struct INS_frame_set_t
{
	uint8_t frameType;
	uint8_t baudrate;
	uint16_t freq;
}INS_Frame_Setting_TypeDef;


typedef enum frame_serial_baud_cfg_t{
	cfg_baud_9600 = 0x01,
	cfg_baud_19200 = 0x02,
	cfg_baud_38400 = 0x03,
	cfg_baud_57600 = 0x04,
	cfg_baud_115200 = 0x05,
	cfg_baud_230400 = 0x06,
	cfg_baud_460800 = 0x07,
	cfg_baud_614400 = 0x08,
}Frame_Serial_Baud_Cfg_TypeDef;

typedef enum frame_serial_freq_cfg_t{
	cfg_freq_1Hz = 0x0001,
	cfg_freq_10Hz = 0x000A,
	cfg_freq_20Hz = 0x0014,
	cfg_freq_25Hz = 0x0019,
	cfg_freq_50Hz = 0x0032,
	cfg_freq_100Hz = 0x0064,
	cfg_freq_125Hz = 0x007D,
	cfg_freq_250Hz = 0x00FA,
	cfg_freq_500Hz = 0x01F4,
}Frame_Serial_Freq_Cfg_TypeDef;

typedef enum frame_serial_format_cfg_t{
	cfg_format_RAWIMU = 0x04,
	cfg_format_GPGGA = 0x03,
	cfg_format_GPRMC = 0x02,
	cfg_format_GIPOT = 0x01,
}Frame_Serial_Format_Cfg_TypeDef;

typedef enum serial_index_t{
	index_RS422 = 0x00,
	index_RS232A = 0x01,
	index_RS232B = 0x02,
}Serial_Index_TypeDef;

typedef  struct setting_data_t
{
    //uint8_t 	baud;
    uint16_t	freq;
    //INS_Frame_Setting_TypeDef serialFrameSetting[3];
    //系统工作状态 2
    //INS_BOOT_MODE_ENUMTypeDef workmode;	//导航工作模式
    //INS_DATA_ENUMTypeDef datamode;		//导航数据模式
    //GNSS臂杆参数 3
    //float gnssMechanicalMigration_x;	//传感器基点相对测量点的机械偏移x
    //float gnssMechanicalMigration_y;	//传感器基点相对测量点的机械偏移y
    //float gnssMechanicalMigration_z;	//传感器基点相对测量点的机械偏移z
    Param_t	param;
    //航向角补偿 4
    //float courseAngleCompensation;
    //用户设置的坐标轴类型 5
    uint8_t imuAxis;
    //时间补偿 6
    short timeCompensation;
    //GNSS基线长度 7
    float gnssBaselineLength;

} SettingDataTypeDef;

typedef struct setting_t{
	uint16_t firstProgram;				//	首次烧写
	uint16_t ProductID;					//	产品ID
	uint16_t DeviceID;					//	设备ID
	uint32_t ChipID[3];					//	器件ID
	
	INS_Frame_Setting_TypeDef serialFrameSetting[3];	//串口输出设置 
	uint16_t imuAxis;					//用户设置的坐标轴类型
	uint16_t gInsSysStatus;				//导航系统工作状态
	INS_DATA_ENUMTypeDef datamode;		//导航数据模式
	INS_BOOT_MODE_ENUMTypeDef workmode;	//导航工作模式
	float courseAngleCompensation;		//航向角补偿
	short timeCompensation;				//时间补偿
	float gnssBaselineLength;			//GNSS基线长度

	float gnssMechanicalMigration_x;	//传感器基点相对测量点的机械偏移x
	float gnssMechanicalMigration_y;	//传感器基点相对测量点的机械偏移y
	float gnssMechanicalMigration_z;	//传感器基点相对测量点的机械偏移z
	
	//uint32_t ARM1_FW_Ver;				//ARM1软件版本
	//uint32_t ARM2_FW_Ver;				//ARM2软件版本
	//uint32_t FPGA_FW_Ver;				//FPGA软件版本
	
	
	//bill 2023-06-25 insert
	uint8_t report_en;					//  上报使能 0:使能 1:禁能
	uint8_t	 calibRate;
    //uint16_t ProductID;					//	产品ID
    //uint16_t DeviceID;					//	设备ID 5
    //uint32_t ChipID[3];					//	器件ID 12
    SettingDataTypeDef 	settingData;
    char facilityType[28];				//设备型号
    char ARM1_FW_Ver[18];				//ARM1软件版本
    char ARM2_FW_Ver[18];				//ARM2软件版本
    char FPGA_FW_Ver[18];				//FPGA软件版本 12
    uint32_t hash;
}AppSettingTypeDef;


typedef union fpga_setting_t{
	uint8_t fpga_setting[100];
	AppSettingTypeDef appSetting;
}FPGA_Setting_TypeDef;

extern uint16_t fpga_data_read_flag;
extern uint16_t fpga_setting_update_flag;
extern FPGA_FRAME_DEF hINSFPGAData;
extern FPGA_FRAME_DEF hINSCANData;
extern AppSettingTypeDef hSetting;
extern AppSettingTypeDef hDefaultSetting;
extern FPGA_Setting_TypeDef hFPGASetting;

extern uint32_t g_week;
extern double g_second;

//typedef enum AppState_t{
//	APP_STATE_INIT = 0,
//	APP_STATE_MEASURE = 1,
//	APP_STATE_CALC = 2,
//	APP_STATE_INTEREACT = 3,
//	APP_STATE_MAX,
//}AppStateTypeDef;

#define APP_SETTING_FLASH_OFFSET		0x400


#define EXMC_BASE_ADDR					0x64000000
#define EXMC_DATA_FILTED_ADDR			(EXMC_BASE_ADDR + 0x100)
#define EXMC_SETTING_ADDR				(EXMC_BASE_ADDR + 0x200)
#define EXMC_RTC_TIME_ADDR				(EXMC_BASE_ADDR + 0x300)

#define SetINS_SYS_Status(x)		hSetting.gInsSysStatus = x
#define GetINS_SYS_Status			hSetting.gInsSysStatus



void save_flash(void);
void read_flash(void);
void caninfupdate(navcanin_t *pcandata);

#endif //____INS_DATA_HX____
