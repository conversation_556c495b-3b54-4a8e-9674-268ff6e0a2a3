/*!
    \file    INS912AlgorithmEntry.c
    \brief   INS912 algorithm interface functions 
    \version v1.0.1
	\author	 Bill
	\date    2023/10/23
*/
#include "InsTestingEntry.h"
#include "string.h"
#include "frame_analysis.h"
#include "EXTERNGLOBALDATA.h"
#include "ins.h"
#include "FUNCTION.h"
arraytodata_t	ginputdata;
gdwrxdata912_t	gins912data;

//const double coe_fog_gx = 1.331368213e-06;//ins9123A小机器
//const double coe_fog_gy = 1.324047945e-06;
//const double coe_fog_gz = 1.326811994e-06;

const double coe_fog_gx = 1.335030071e-06;//ins912调查局机器
const double coe_fog_gy = 1.319549770e-06;
const double coe_fog_gz = 1.324544541e-06;

//void InsTestingEntry(unsigned short *pfpgadata, navcanin_t *pcanin, navoutdata_t *pnavout)
void INS912AlgorithmEntry(unsigned short *pfpgadata, navcanin_t *pcanin, navoutdata_t *pnavout)
{
	
	memcpy(ginputdata.gddata, pfpgadata, sizeof(ginputdata.gddata));
	memcpy(&gins912data, &ginputdata.wcdata, sizeof(gins912data));

	pnavout->accelX = gins912data.accelerometerx;
	pnavout->accelY = gins912data.accelerometery;
	pnavout->accelZ = gins912data.accelerometerz;
	
	//pnavout->gyroX = gins912data.fogx / 300000.0;
	//pnavout->gyroY = gins912data.fogy / 300000.0;
	//pnavout->gyroZ = gins912data.fogz / 300000.0;
	
	pnavout->gyroX = gins912data.fogx*coe_fog_gx;
	pnavout->gyroY = gins912data.fogy*coe_fog_gy; 
	pnavout->gyroZ = gins912data.fogz*coe_fog_gz;
	
	pnavout->latitude = gins912data.latitude;
	pnavout->longitude = gins912data.longitude;
	pnavout->altitude = gins912data.altitude;

	pnavout->roll = gins912data.roll;
	pnavout->pitch = gins912data.pitch;
	pnavout->azimuth = gins912data.yaw;

	pnavout->ve = gins912data.eastvelocity;
	pnavout->vn = gins912data.northvelocity;
	pnavout->vu = gins912data.upvelocity;
	
	pnavout->status = gins912data.rtkstatus >> 8;	//debug...
	pnavout->gps_week = gins912data.gnssweek;
	pnavout->gps_time = gins912data.secondofweek;
	
	ggpsorgdata.rtkStatus = gins912data.rtkstatus >> 8;
	ggpsorgdata.gpsweek = gins912data.gnssweek;
	ggpsorgdata.gpssecond = gins912data.secondofweek;
	ggpsorgdata.baseline = gins912data.baselength * 1E-9;
	ggpsorgdata.StarNum = gins912data.gpsstarnumber;
	
	memset(&paochedata,0,sizeof(paochedata));
	fmc2sinsraw(gfpgadata,&paochedata);
	//if(fabs(paochedata.gnss_tow-paochedata.gnss_tow2) <10000)
	//if(paochedata.dat_len==0x63 && paochedata.cfg==36 && paochedata.ver==257)
	if(inscanruning==true && fabs(paochedata.gnss_tow-paochedata.gnss_tow2) <10000 && 
		 fabs(paochedata.fog_gx)>0 && fabs(paochedata.fog_gy)>0&&fabs(paochedata.fog_gz)>0&&
	   fabs(paochedata.fog_ax)>0 && fabs(paochedata.fog_ay)>0&&fabs(paochedata.fog_az)>0)
	{
		ins_update();
	}
	//主工作流程在这里设置
	switch( g_SysVar.WorkPhase )        //工作阶段
  {
			case PHASE_STANDBY :
			{
					if( g_SysVar.isAlign_Mode_Set == YES )
					{
							if( (g_SysVar.isAlignOnBoard == NO) && (g_InitBind.isBind == YES) )
							{   //惯性凝固坐标系对准初始化
									InertialSysAlign_Init( &g_InitBind,&g_InertialSysAlign );   
									g_SysVar.WorkPhase = PHASE_COARSE_ALIGN;    //粗对准
							}
							if( (g_SysVar.isAlignOnBoard == YES) && (g_InitBind.isBind == YES)&&(g_InitBind.isHeadBind == YES))
							{
									double r_Pos[3];
									r_Pos[0] = g_GNSSData_VMC.r_GNSSLati;
									r_Pos[1] = g_GNSSData_VMC.r_GNSSLogi;
									r_Pos[2] = g_GNSSData_VMC.GNSSHeight;
									DynamicInertialSysAlign_Init( r_Pos, g_GNSSData_VMC.GNSSVn,&g_DynamicInertialSysAlign );
									g_SysVar.WorkPhase = PHASE_COARSE_ALIGN;    //粗对准
							}
					}
					break;
			 }               
			 case PHASE_COARSE_ALIGN :
			 {   
					if(g_SysVar.isAlignOnBoard == NO)
					{
						//惯性凝固粗对准计算
						InertialSysAlignCompute(r_Gyro,r_LastGyro, Acc,LastAcc,&g_InertialSysAlign);
						/*if(g_InertialSysAlign.AlignTime > g_InertialSysAlign.T1 + 3.0)
						{
								FinishInertialSysAlign(&g_InertialSysAlign);
						}*/
						if(g_InertialSysAlign.AlignTime >= TIME_COARSE_ALIGN)
						{
								//计算对准参数
								FinishInertialSysAlign(&g_InertialSysAlign);
								//导航初始化
								Navi_Init(&g_InitBind, &g_InertialSysAlign, &g_Navi);  
						}
					}
					else
					{
							double r_Pos[3];
							r_Pos[0] = g_GNSSData_VMC.r_GNSSLati;
							r_Pos[1] = g_GNSSData_VMC.r_GNSSLogi;
							r_Pos[2] = g_GNSSData_VMC.GNSSHeight;
							DynamicInertialSysAlignCompute(r_Gyro,r_LastGyro,Acc,LastAcc,r_Pos,g_GNSSData_VMC.GNSSVn, &g_DynamicInertialSysAlign);
							if(g_DynamicInertialSysAlign.AlignTime >= TIME_FAST_ALIGN)
							{
									//计算对准参数
									FinishDynamicInertialSysAlign(&g_DynamicInertialSysAlign);
									//导航初始化
									DynamicNavi_Init(&g_GNSSData_VMC, &g_DynamicInertialSysAlign, &g_Navi);
							}
					}
					break;
			}
			case PHASE_FINE_ALIGN:
			{
				 
			
					break;
			}
		  case PHASE_INS_NAVI :
			{
          //导航计算
          NaviCompute(r_Gyro,r_LastGyro,Acc,LastAcc,&g_SysVar,&g_ADCData,&g_AttiCorrVar,&g_Navi);
          //滤波用矩阵的累积
          ComputeFn(&g_Navi, g_Kalman.Fn);
          
          g_SysVar.Time_INS_Alone += TIME_NAVI;

          if(g_SysVar.isDampedOK == YES)
          {
              ComputeFk(g_Kalman.Fn, g_Kalman.Fk);//							
              KalPredict(&g_Kalman);
              ErrCorrect_1_Navi_Time_For_INS(&g_Navi, &g_Kalman);                                
          }          
			    break;
			}
      case PHASE_INTEGRATED_NAVI :
      {
          //导航计算
          NaviCompute(r_Gyro,r_LastGyro,Acc,LastAcc,&g_SysVar,&g_ADCData,&g_AttiCorrVar,&g_Navi);
          //滤波用矩阵的累积
          ComputeFn(&g_Navi, g_Kalman.Fn);
          
          g_SysVar.Time_Integrated_Navi += TIME_NAVI;
            
          if(g_SysVar.Time_Integrated_Navi >= 20.0)
          {
             g_SysVar.isDampedOK = YES;
          }

            if(g_Kalman.isCorrectError == YES)
            {
                //误差校正
                ErrStore_1s(&g_Navi, &g_Kalman,&g_GNSSData_In_Use);

                g_Kalman.isCorrectError = NO;
                //Gnss_ins_float.other_State &= ~STATE_GPS_REFRESH;
            }
                if(g_Kalman.isKalmanStart == YES)
                {
//                    int a;
//									  a = 1;
               			//进行Kalman滤波主体部分的计算，该部分计算量较大									  
                    KalCompute( &g_GNSSData_In_Use, &g_Kalman);
									  if(g_Kalman.State == 4)
										{
											g_Kalman.isKalmanStart  = NO;   //信号清除
                      g_Kalman.isCorrectError = YES;  //开启修正误差信号
											g_Kalman.State = 0;
										}
										//a = 0;
               
               }
							break;
			}
      default:
      {
          g_SysVar.WorkPhase = PHASE_FAIL;    //故障状态
      }
    }
		//协议测试数据...
	#if 1
	//pnavout->longitude = 22.123456;
	//pnavout->latitude = 113.654321;
	//pnavout->altitude = 12.12345;
	//pnavout->ve = 11.111;
	//pnavout->vn = 22.222;
	//pnavout->vu = 33.333;
	//pnavout->pitch = 44.444;
	//pnavout->roll = 55.555;
	//pnavout->azimuth = 66.666;
	
	pnavout->roll     = (float)ins_state.roll;
	pnavout->pitch    = (float)ins_state.pitch;
	pnavout->azimuth  = (float)ins_state.yaw;
	
  pnavout->vn     = (float)ins_state.vn;
	pnavout->ve    = (float)ins_state.ve;
	pnavout->vu  = -(float)ins_state.vd;
	
	pnavout->latitude     = ins_state.lat;
	pnavout->longitude    = ins_state.lon;
	pnavout->altitude     = -ins_state.hgt;
	#endif
}


