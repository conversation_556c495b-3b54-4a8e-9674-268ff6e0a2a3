/***********************************************************************************
nav Mahony module
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-5-9          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#include <string.h>
#include "nav_includes.h"

_NAV_MAHONY_t  g_NAV_MAHONY={0};

//IIR滤波器
//Butterworth lowpass IIR filter
//设计为4阶低通滤波器
void IIRFilter()
{
	
}

//线性插值
//X为两点横坐标，Y为两点纵坐标，value插值点横坐标，返回插值点纵坐标
double interplim(double *X,double *Y, double value)
{
	double kk;
	if(X[1]==X[0])
	{
#ifdef linux
		inav_log(INAVMD(LOG_ERR),"X[0]==X[1]=%f\r\n",X[0]);
#endif
		return Y[0];
	}
	kk=(Y[1]-Y[0])/(X[1]-X[0]);
	//保证value在X中间
	if(		(value>=X[0]&&value<X[1])
		||	(value<X[0]&&value>=X[1])
		)
	{
		return Y[0]+kk*(value-X[0]);
	}
	else
	{
#ifdef linux
		inav_log(INAVMD(LOG_ERR),"value is not between X[0] and X[1]\r\n");
#endif
		return Y[0]+kk*(value-X[0]);
	}
}

void SetPidParmByMotion(_NAV_Data_Full_t* NAV_Data_Full_p,_NAV_MAHONY_t *pNAV_MAHONY)
{
	_IIR_Filter_t mIIR_Filter={0};
	if(E_AHRS_ADAPTIVE_YES == pNAV_MAHONY->adaptive)
	{
		//计算加计误差
		//对加计进行IIR滤波
		
		
	}
	else if(E_AHRS_ADAPTIVE_NO == pNAV_MAHONY->adaptive)
	{
	}
	else
	{
#ifdef linux
		inav_log(INAVMD(LOG_ERR),"Error g_NAV_MAHONY->adaptive=%d\r\n",pNAV_MAHONY->adaptive);
#endif
	}
}
void MahonyInit(_NAV_MAHONY_t *pNAV_MAHONY)
{
	memset(pNAV_MAHONY,0,sizeof(_NAV_MAHONY_t));
}

void MahonyUpdate(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	int i=0;
	double tmp_acc[3]={0};
	double err_acc[3]={0};
	/**********************计算加速度计修正量**************************/
	//由于导航系N是东北天，机体系为右前上，
	//如果加速度计坐标系与机体系一致，
	//载体静止且水平时加速度计测量值应为[0,0,gn]，
	//Z方向为正（由于加速度计测的实际是1g的支持力，而非重力）。
	//利用Cnb矩阵将地球矢量转到机体系右前上，得到vx, vy, vz。
	matmul("NN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Cn2b, NAV_Data_Full_p->EARTH.gn, 0.0, tmp_acc);
	//将实际输出加计与tmp_acc求向量积
	cross3(tmp_acc,NAV_Data_Full_p->IMU.acc_use,err_acc);
	
	
	double tmp_mag[3]={0};
	double tmp1_mag[3]={0};
	double tmp2_mag[3]={0};
	double err_mag[3]={0};
	/**************************计算磁力计修正量************************/
	//如果不考虑误差，磁力计的测量值经过正确Cbn转到地理系，理论上水平方向上只有北向有值，
	//因此地球磁场的切线在南北方向上。
	//但实际上由于航向不准，造成Cbn有误差，所以转换后的磁力计测量值在北向和东向都有值。
	//互补滤波算法中，先将磁力计的值用Cbn转到地理系：
	matmul("NN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Cb2n, NAV_Data_Full_p->MAGNET.mag_use, 0.0, tmp_mag);
	//由于在水平方向，无论Cbn在航向上有没有误差，
	//转换后水平方向矢量和应该相等。
	tmp1_mag[0]=0;
	tmp1_mag[1]=sqrt(tmp_mag[0]*tmp_mag[0]+tmp_mag[1]*tmp_mag[1]);
	tmp1_mag[2]=tmp_mag[2];
	//再转回载体坐标系
	matmul("NN", 3, 1, 3, 1.0, NAV_Data_Full_p->SINS.Cn2b, tmp1_mag, 0.0, tmp2_mag);
	//将实际输出与tmp2_mag做向量积
	cross3(tmp2_mag,NAV_Data_Full_p->MAGNET.mag_use,err_mag);

	//double kp=0.0;
	//double ki=0.0;
	double err_both[3]={0.0};
	double deltap[3]={0.0};
	double deltai[3]={0.0};
	/*************************修正陀螺仪输出值*****************************/
	matrixSum(err_acc, err_mag, 3, 1, 1.0, err_both);
	//根据pi调节，设置对陀螺测量值的修正量：
	//根据当前运动情况,设置kp,ki
	SetPidParmByMotion(NAV_Data_Full_p,&g_NAV_MAHONY);
	//P调节修正量
	for(i=0;i<3;i++)
	{
		deltap[i]=g_NAV_MAHONY.Kp*err_both[i];
	}
	//i调节修正量
	for(i=0;i<3;i++)
	{
		deltai[i]=g_NAV_MAHONY.Ki*err_both[i]*NAV_Data_Full_p->SINS.ts;
	}	
	//对陀螺仪测量值进行修正：
	for(i=0;i<3;i++)
	{
	 	NAV_Data_Full_p->IMU.gyro_use[i]=NAV_Data_Full_p->IMU.gyro_use[i]+deltap[i]+deltai[i];
	}

	double phim[3]={0.0};
	//计算姿态
	for(i=0;i<3;i++)
	{
		phim[i]=NAV_Data_Full_p->IMU.gyro_use[i]*NAV_Data_Full_p->SINS.ts;//rad
	}
	
	SINS_UP_ATT(NAV_Data_Full_p,phim);
	
}



