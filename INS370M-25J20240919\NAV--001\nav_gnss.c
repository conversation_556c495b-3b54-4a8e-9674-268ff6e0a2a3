/***********************************************************************************
nav gnss module
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-5-22          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#include "nav_includes.h"

unsigned char gps_data_check(_NAV_Data_Full_t* NAV_Data_Full_p);



/******************************************************************************
*原  型：u8 gps_data_check(_NAV_Data_Full_t* NAV_Data_Full_p)
*功  能：gps数据检测
*输  入：无
*输  出：无
*******************************************************************************/
unsigned char gps_data_check(_NAV_Data_Full_t* NAV_Data_Full_p)
{
	unsigned char abnormal_flag = RETURN_SUCESS;
	int difftime=0;
	difftime = NAV_Data_Full_p->GPS.gpssecond-NAV_Data_Full_p->GPS.gpssecond_old;

	if(E_NAV_STATUS_START!=NAV_Data_Full_p->Nav_Status)
	{
		if((difftime<0)||(difftime>(1000/SAMPLE_FREQ_GNSS)))
		{
#ifdef linux
			inav_log(INAVMD(LOG_ERR),"gps_data_check:gpssecond=%d,gpssecond_old=%d\r\n"\
			,NAV_Data_Full_p->GPS.gpssecond,NAV_Data_Full_p->GPS.gpssecond_old);
#endif
			abnormal_flag = RETURN_FAIL;
		}
	}
	return abnormal_flag;
}

void GNSS_init()
{
	
}
void Get_GNSS_Data(_NAV_Data_Full_t* NAV_Data_Full_p,CombineDataTypeDef* CombineData_p)
{
	//GPS
	NAV_Data_Full_p->GPS.gpssecond_old = NAV_Data_Full_p->GPS.gpssecond;
	if(NAV_Data_Full_p->GPS.gpssecond_old != CombineData_p->gnssInfo.gpssecond)
	{

/************************************判断当前位置所在半球*******************************************/	
		if(E_LAT_SOUTH_Hemisphere == CombineData_p->gnssInfo.LatHemisphere)
		{
			NAV_Data_Full_p->GPS.Lat = -CombineData_p->gnssInfo.Lat;
		}
		else
		{
			NAV_Data_Full_p->GPS.Lat = CombineData_p->gnssInfo.Lat;
		}
		
		if(E_LON_WEST_Hemisphere == CombineData_p->gnssInfo.LonHemisphere)
		{
			NAV_Data_Full_p->GPS.Lon = -CombineData_p->gnssInfo.Lon;
		}
		else
		{
			NAV_Data_Full_p->GPS.Lon = CombineData_p->gnssInfo.Lon;
		}
		
		NAV_Data_Full_p->GPS.Altitude = CombineData_p->gnssInfo.Altitude;
		NAV_Data_Full_p->GPS.ve = CombineData_p->gnssInfo.ve;
		NAV_Data_Full_p->GPS.vn = CombineData_p->gnssInfo.vn;
		NAV_Data_Full_p->GPS.vu = CombineData_p->gnssInfo.vu;
		NAV_Data_Full_p->GPS.Pitch = CombineData_p->gnssInfo.Pitch;
		NAV_Data_Full_p->GPS.Roll = CombineData_p->gnssInfo.Roll;
		NAV_Data_Full_p->GPS.Heading = CombineData_p->gnssInfo.Heading;
		NAV_Data_Full_p->GPS.trackTrue = CombineData_p->gnssInfo.trackTrue;
		NAV_Data_Full_p->GPS.Position_Status = CombineData_p->gnssInfo.PositioningState;
		//NAV_Data_Full_p->GPS.rtkStatus = CombineData_p->gnssInfo.rtkStatus;
		//kinematic 定位rtk状态
		SetNavRtkStatus(CombineData_p->gnssInfo.rtkStatus);
		//movingbase 定向rtk状态
		NAV_Data_Full_p->GPS.headingStatus = CombineData_p->gnssInfo.headingStatus;
		NAV_Data_Full_p->GPS.Sate_Num = CombineData_p->gnssInfo.StarNum;
		NAV_Data_Full_p->GPS.delay_pps = CombineData_p->gnssInfo.ppsDelay*0.001;
		NAV_Data_Full_p->GPS.gpsweek = CombineData_p->gnssInfo.gpsweek;
		NAV_Data_Full_p->GPS.gpssecond = CombineData_p->gnssInfo.gpssecond;
		
	}

	//gps data check
	if(RETURN_FAIL == gps_data_check(&NAV_Data_Full))
	{
		//SetNavStatus(E_NAV_STATUS_START);		
	}
	
}



