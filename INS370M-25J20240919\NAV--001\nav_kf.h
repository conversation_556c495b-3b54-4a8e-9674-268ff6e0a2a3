/***********************************************************************************
kalman filter proccesing header
All rights reserved I-NAV 2023 2033
***********************************************************************************/
/***********************************************************************************
Modification history

|-----------+---------------+-------------------|
|Author          |    Date               |    Done                      |
|-----------+---------------+-------------------|
|DengWei       |  2023-2-4          | First Creation             |
|-----------+---------------+-------------------|  
***********************************************************************************/
#ifndef __NAV_KF_H__
#define __NAV_KF_H__
//#include "NAV_MCU.h"
#include "nav_type.h"

 
void KF_Init(_NAV_Data_Full_t* NAV_Data_Full_p);
void KF_UP2(_NAV_Data_Full_t* NAV_Data_Full_p);
double CorrHeading(double orginheading);
void TEST_UP(_NAV_Data_Full_t* NAV_Data_Full_p);

#endif