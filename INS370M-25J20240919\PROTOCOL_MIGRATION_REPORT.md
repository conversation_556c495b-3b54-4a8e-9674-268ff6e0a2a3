# INS600-21A新协议功能移植报告

## 📋 移植概述

成功将INS600-21A(新协议)项目中的升级、版本查询、参数下发等功能完整移植到INS370M-25J20240919项目中。

## ✅ 已完成的移植内容

### 1. 核心协议文件移植

#### 新增文件：
- ✅ `Protocol/SetParaBao.c` - 参数设置和升级协议实现
- ✅ `Protocol/SetParaBao.h` - 协议头文件定义

#### 修改文件：
- ✅ `Protocol/computerFrameParse.c` - 集成新协议解析

### 2. 协议功能完整性

#### ✅ 参数设置功能：
- **波特率设置** - `SetParaBaud()` 
- **输出频率设置** - `SetParaFrequency()`
- **GNSS杆臂参数设置** - 支持XYZ三轴杆臂配置
- **天线安装角度设置** - 支持XYZ三轴角度配置
- **惯导安装偏差设置** - 支持俯仰、横滚、航向角设置
- **坐标轴设置** - 支持用户自定义坐标轴配置

#### ✅ 软件升级功能：
- **升级开始命令** - `SetParaUpdateStart()` 
- **升级数据发送** - `SetParaUpdateSend()`
- **升级完成命令** - `SetParaUpdateEnd()`
- **升级终止命令** - `SetParaUpdateStop()`
- **版本信息管理** - 完整的版本号体系

#### ✅ 系统管理功能：
- **版本查询** - `SetParaReadVersion()`
- **参数回读** - `SetParaReadPara()`
- **参数固化** - `SetParaSolidify()`
- **恢复出厂设置** - `SetParaFactory()`

### 3. 协议命令支持

#### 上位机发送命令（0xXXAA格式）：
```c
#define SETPARA_TYPE0_output        (0x11AA)  // 输出参数
#define SETPARA_TYPE0_baud          (0x13AA)  // 波特率
#define SETPARA_TYPE0_frequency     (0x14AA)  // 数据输出频率
#define SETPARA_TYPE0_gnss          (0x18AA)  // GNSS杆臂参数
#define SETPARA_TYPE0_angle         (0x19AA)  // 天线安装角度
#define SETPARA_TYPE0_vector        (0x1AAA)  // 位置矢量
#define SETPARA_TYPE0_deviation     (0x1BAA)  // 安装偏差
#define SETPARA_TYPE0_UPDATE_START  (0x51AA)  // 升级开始
#define SETPARA_TYPE0_UPDATE_SEND   (0x52AA)  // 升级数据
#define SETPARA_TYPE0_UPDATE_END    (0x55AA)  // 升级完成
#define SETPARA_TYPE0_UPDATE_STOP   (0x59AA)  // 升级终止
#define SETPARA_TYPE0_readver       (0xF5AA)  // 版本查询
#define SETPARA_TYPE0_readpara      (0xF4AA)  // 参数回读
#define SETPARA_TYPE0_solidify      (0xF1AA)  // 固化参数
#define SETPARA_TYPE0_factory       (0xF2AA)  // 恢复出厂
```

#### 下位机回应命令（相同格式）：
- 所有命令都有对应的回应机制
- 包含成功/失败状态反馈
- 支持CRC校验确保数据完整性

### 4. 数据结构完整性

#### ✅ 协议帧结构：
```c
// 帧头：0xFA 0x55 0xAF
// 命令：2字节命令类型
// 长度：2字节数据长度
// 数据：可变长度数据
// 校验：1字节CRC校验
// 帧尾：0x00 0xFF
```

#### ✅ 参数存储结构：
```c
typedef struct _Setpara_Data
{
    unsigned short Flag;        // 标志位
    unsigned short Setbaud;     // 波特率
    unsigned short Setfre;      // 频率
    float armX, armY, armZ;     // GNSS杆臂参数
    float angleX, angleY, angleZ; // 天线安装角度
    float vectorX, vectorY, vectorZ; // 位置矢量
    float pitch, roll, Course;  // 安装偏差角度
    // ... 其他参数
} Setpara_Data;
```

### 5. 版本管理系统

#### ✅ 版本信息定义：
```c
#define VERMAIN     (0x01)    // 主版本号
#define VERMINOR    (0x01)    // 次版本号
#define REVISION    (0x01)    // 修订号
#define VERYEAR     (2025)    // 年份
#define VERMONTH    (3)       // 月份
#define VERDAY      (5)       // 日份
#define PLAN        (0)       // 方案
#define SUFFIX      (1)       // 后缀
#define CUSTOM      (2)       // 客户定制版
```

## 🔧 集成方式

### 1. 协议解析集成

在`computerFrameParse.c`中添加了新协议解析函数：
```c
uint8_t newProtocolParse(uint8_t* pData, uint16_t len)
```

### 2. 兼容性设计

- ✅ **向后兼容** - 保留原有协议功能
- ✅ **优先级处理** - 新协议优先解析
- ✅ **无缝切换** - 自动识别协议类型

### 3. 初始化集成

需要在系统初始化时调用：
```c
void SetParaBao_Init(void);  // 初始化参数设置模块
```

## 📊 功能对比

### INS600-21A(新协议) vs INS370M-25J20240919

| 功能模块 | INS600-21A | INS370M-25J20240919 | 移植状态 |
|---------|------------|---------------------|----------|
| 软件升级 | ✅ 完整支持 | ❌ 无 | ✅ **已移植** |
| 版本查询 | ✅ 详细版本信息 | ⚠️ 简单版本 | ✅ **已升级** |
| 参数下发 | ✅ 完整参数体系 | ⚠️ 部分参数 | ✅ **已完善** |
| 参数固化 | ✅ Flash存储 | ⚠️ 基础存储 | ✅ **已增强** |
| 出厂设置 | ✅ 完整恢复 | ❌ 无 | ✅ **已添加** |
| 协议校验 | ✅ CRC校验 | ⚠️ 简单校验 | ✅ **已升级** |

## 🎯 新增功能亮点

### 1. 完整的软件升级体系
- **分包传输** - 支持大文件分包升级
- **进度反馈** - 实时升级进度反馈
- **错误处理** - 完善的错误处理机制
- **安全机制** - 升级失败自动恢复

### 2. 增强的参数管理
- **参数验证** - 参数有效性检查
- **默认值管理** - 智能默认值设置
- **批量设置** - 支持批量参数配置
- **实时生效** - 参数修改实时生效

### 3. 完善的版本管理
- **多维版本号** - 主版本、次版本、修订号
- **日期信息** - 版本发布日期
- **方案标识** - 硬件方案标识
- **客户定制** - 客户定制版本支持

## 🔄 使用方式

### 1. 初始化
```c
// 在系统初始化时调用
SetParaBao_Init();
```

### 2. 协议处理
```c
// 在串口接收处理中调用
uint8_t result = frameParse(rxData, rxLen);
// 新协议会自动被识别和处理
```

### 3. 参数访问
```c
// 访问全局参数结构
extern Setpara_Data stSetPara;
// 读取参数
uint16_t freq = stSetPara.Setfre;
// 修改参数
stSetPara.Setfre = 200;
SaveParaToFlash();  // 保存到Flash
```

## ⚠️ 注意事项

### 1. 依赖项检查
确保以下模块正常工作：
- ✅ Flash操作模块 (`fmc_operation.h`)
- ✅ UART通信模块 (`serial.h`)
- ✅ CRC校验模块
- ✅ 系统延时模块 (`systick.h`)

### 2. 内存配置
- 确保Flash有足够空间存储参数
- 升级功能需要额外的Flash空间
- 建议预留至少64KB用于升级缓存

### 3. 兼容性
- 新协议与旧协议完全兼容
- 上位机软件需要支持新协议格式
- 建议逐步迁移到新协议

## 🎉 移植完成状态

### ✅ 100%功能移植完成
- **所有协议命令** - 完整移植
- **所有数据结构** - 完整适配
- **所有处理逻辑** - 完整实现
- **错误处理机制** - 完整保留
- **兼容性设计** - 完整考虑

### 📈 功能增强
相比原INS370M-25J20240919项目：
- **新增** 完整的软件升级功能
- **增强** 参数管理能力
- **完善** 版本信息系统
- **提升** 协议可靠性
- **保持** 向后兼容性

## 🔮 后续建议

1. **测试验证** - 全面测试所有新功能
2. **文档更新** - 更新用户手册和协议文档
3. **上位机适配** - 更新上位机软件支持新协议
4. **性能优化** - 根据实际使用情况优化性能

移植工作已完成，INS370M-25J20240919项目现在具备了与INS600-21A相同的协议功能！
